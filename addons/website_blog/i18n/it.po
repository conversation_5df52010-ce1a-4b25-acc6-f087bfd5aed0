# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# <PERSON>il <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr "#{year}"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "' intestazione di pagina."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "'. Mostrare risultati per '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- Tutte le date"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>I binocoli sono leggeri e portatili.</b> A meno che tu abbia il lusso di "
"realizzare e gestire un tuo osservatorio, dovrai probabilmente viaggiare per"
" filmare viste. I binocoli sono semplici da portare con sé, anche per il "
"peso, e da utilizzare rispetto ad un kit ingombrante di allestimento."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>Scegli le competenze di un esperto</b>. Se non sei già familiare con i "
"club o società di astronomia, gli addetti alle vendite dei negozi di "
"telescopi saranno in grado di indirizzarti verso le società attive nella tua"
" zona. Una volta che avrai stabilito un legame con le persone che hanno già "
"acquistato telescopi potrai farti un'idea di cosa funziona e cosa bisogna "
"evitare invece di affidarti semplicemente ad un articolo online o agli "
"addetti alle vendite di un grande magazzino."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr "<b>Pubblica il tuo articolo</b> per renderlo visibile ai visitatori."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>Accedi</b>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>Provalo prima di comprarlo.</b> Un altro vantaggio dell'esplorare il "
"campo. Puoi paassare alcune ore in compagnia di persone che conoscono i "
"telescopi e sanno come manovrare l'attrezzatura per imparare gli aspetti "
"tecnici chiave e testarli prima di investire denaro nel proprio impianto."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>Scrivi qui la tua storia.</b> Usa la barra degli strumenti superiore per "
"definire lo stile del tuo testo: aggiungi un'immagine o una tabella, scrivi "
"in grassetto o in corsivo, ecc. Trascina e rilascia i componenti per avere "
"un blog  con più elementi visivi."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">Oltre alla popolazione autoctona, anche la fauna "
"selvatica è una delle principali attrattive per il pubblico.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""
"<em class=\"h4 my-0\">È di fondamentale importanza che tu abbia il "
"telescopio giusto.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr ""
"<em class=\"h4 my-0\">Quel momento \"Wow\" è ciò di cui si occupa "
"l'astrologia.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">Più recensioni si leggono, più si nota come tendano a "
"raggrupparsi agli estremi delle opinioni.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">C'è qualcosa di senza tempo nel cosmo.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">Il tuo studio della luna, come qualsiasi altra cosa, "
"può andare dal semplice al molto complesso.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>Nessuna etichetta definita</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"Al contenuto del blog\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"GitHub\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Sito web\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"YouTube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Leggi "
"successivo</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"me-1\">Show:</span>"
msgstr "<span class=\"me-1\">Mostra:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled ps-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled ps-0\">Blog:</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "Un ottimo modo per scoprire luoghi nascosti"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"Una vacanza nel Copper Canyon promette di essere un mix eccitante di relax, "
"cultura, storia, fauna selvatica ed escursioni."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "Pubblicato un nuovo articolo"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"Un viaggiatore può scegliere di esplorare la zona camminando lungo il canyon"
" o avventurandosi all'interno. È richiesto un piano dettagliato per coloro "
"che vogliono esplorare il canyon in profondità. Esistono numerose agenzie di"
" viaggio specializzate nell'organizzazione di tour nella regione. I "
"visitatori possono volare verso il Copper Canyon utilizzando un visto "
"turistico, valido per 180 giorni. Inoltre, è possibile anche partire da "
"qualsiasi punto degli Stati Uniti e ottenere il visto presso la stazione "
"doganale messicana di confine."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "Chi siamo"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"Innanzitutto, <b>rivolgiti ad un negozio di telescopi rispettabile</b> con "
"personale qualificato. Se compri il tuo telescopio da Wal-Mart o in un "
"grande magazzino, le possibilità di acquistare un buon prodotto sono molto "
"ridotte."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "Accedi all'articolo"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Attivo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "Aggiungi alcuni"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "Tutti"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "Tutti i blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "Tutti i blog"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Alone in the ocean"
msgstr "Soli nell'oceano"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "A questo proposito, quanto è difficile il montaggio e lo smontaggio?"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/website_blog.js:0
#, python-format
msgid "Amazing blog article: %s! Check it live: %s"
msgstr "Incredibile articolo di blog: %s! Controlla dal vivo: %s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"Una emozionante miscela di relax, cultura, storia, natura ed escursionismo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"E quando tutto è detto e fatto, <b>attrezzati</b>. La tua ricerca di "
"telescopi sempre nuovi e migliori durerà per tutta la vita. Appassionati "
"dell' astronomia e l'esperienza arricchirà ogni aspetto della tua vita. Sarà"
" una passione che non vorrai più perdere."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"Un'altra caratteristica unica del Copper Canyon è la presenza della cultura "
"indiana Tarahumara. La popolazione seminomade vive in abitazioni scavate "
"nelle caverne e i loro principali mezzi di sostentamento sono l'agricoltura "
"e l'allevamento di bestiame."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archive"
msgstr "Archivio"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "In archivio"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archives"
msgstr "Archivi"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "articolo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "articoli"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "Astronomia"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"I club di astronomia sono luoghi vivaci pieni di amatori competenti che "
"amano condividere le loro conoscenze con te. Per il prezzo di una coca cola "
"e degli snack, andranno a guardare le stelle con te e ti sommergeranno di "
"banalità e grandi conoscenze."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "L'astronomia è \"celeste\""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Flusso Atom"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Autore"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "Nome autore"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "Avatar"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Awesome hotel rooms"
msgstr "Camere d'albergo impressionanti"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "Fai attenzione a questa cosa chiamata \"astronomia\""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"Entrare a far parte della società degli astronomi dilettanti ti permetterà "
"di accedere a questi sforzi organizzati per raggiungere nuovi livelli nella "
"nostra capacità di studiare la Luna terrestre. Inoltre, ti darà la "
"possibilità di avere amici e coetanei che condividono la passione per "
"l'astronomia e che possono condividere la loro esperienza e le loro aree di "
"competenza mentre cerchi di capire dove guardare nell'immenso cielo "
"notturno, sulla Luna e oltre, nella ricerca di conoscenza dell'universo "
"apparentemente infinito che ci sovrasta."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "Entra a far parte della società degli appassionati di astronomia."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "Strutture camere da letto"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"Prima di effettuare una spesa così grande, potrebbe essere una buona idea "
"investire in un buon binocolo. Esistono persino binocoli fatti per osservare"
" le stelle, che fanno un lavoro altrettanto buono per darti quella visione "
"in più di cui hai bisogno per vedere un po' meglio le meraviglie "
"dell'universo. Un binocolo ben progettato fornisce una maggiore mobilità e "
"la possibilità di avere una \"visione migliorata\" a portata di mano quando "
"ne hai bisogno."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "Prima di fare il tuo primo acquisto..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "Al di là degli occhi"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog"
msgstr "Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "Nome blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blog Page"
msgstr "Pagina blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog Post"
msgstr "Articolo blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "Copertina articolo blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_post
msgid "Blog Post Pages"
msgstr "Pagine post di blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Titolo per articolo blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_post_pages
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blog Posts"
msgstr "Articoli blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Sottotitolo blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Etichetta blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "Categoria etichetta blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "Etichette blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "Titolo blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "Titolo del blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "Blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs List"
msgstr "Elenco blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs Page"
msgstr "Pagina blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Bottom"
msgstr "In basso"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Breadcrumb"
msgstr "Percorso di navigazione"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"Come passare al setaccio tutte le fantastiche offerte che ci vengono "
"proposte? Ti fidi davvero delle fotografie e descrizioni di hotel valutate "
"dalle agenzie stesse con lo scopo di ottenere più prenotazioni? Le "
"recensioni dei viaggiatori possono essere utili ma bisogna essere cauti: "
"spesso non sono obiettive e non adatte ai tuoi interessi. Come fate a sapere"
" se le caratteristiche importanti per l'autore della recensione sono "
"importanti anche per voi?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "Acquistare un telescopio"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"Comprare il telescopio giusto per spingere il tuo amore per l'astronomia al "
"livello successivo è un grande passo avanti nello sviluppo della tua "
"passione per le stelle."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "Può pubblicare"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cards"
msgstr "Schede"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Categoria"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "Servizi per bambini"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Choose an image from the library."
msgstr "Scegli un immagine dalla libreria."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Click here to add new content to your website."
msgstr "Fai clic qui per aggiungere nuovi contenuti al sito web."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""
"Clicca su \"<b>Nuovo</b>\" nell'angolo in alto a destra per scrivere il tuo "
"primo post sul blog."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "Chiudi"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "Commento"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments"
msgstr "Commenti"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments/Views Stats"
msgstr "Commenti/statistiche visualizzazioni"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Contenuto"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"Il Copper Canion è una delle sei gole presenti nell'area. Sebbene il nome "
"faccia allusione all'attività di estrazione del rame, non è questo il caso. "
"Infatti, il nome deriva dal rame e dai licheni  verdi che ricoprono il "
"canion. Presenta due zone climatiche: nella parte alta c'è un clima alpino "
"mentre al sud il clima è subtropicale. Gli inverni sono freddi con tempeste "
"di neve frequenti in alta quota; invece l'estate è asciutta e molto calda. "
"La capitale, Chihuahua, è un deserto in alta quota dove il meteo è "
"caratterizzato da inverni freddi ed estati più calde. La regione è unica "
"proprio per via della varietà degli ecosistemi che la caratterizzano."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cover"
msgstr "Copertina"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "Proprietà copertina"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Data (nuova a vecchia)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Data (vecchia a nuova)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "Descrizione"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "Dexter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Drop Zone for Building Blocks"
msgstr "Area di rilascio blocchi di costruzione"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "Maui orientale"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"I tour in elicottero nella parte est dell'isola ti permetteranno di vedere "
"il vulcano Haleakala o \"casa del sole\", alto 3,055 metri. Si tratta di un "
"vulcano inattivo la cui ultima eruzione risale al 1790. Potrai vedere il "
"cratere del vulcano e la terra arida e secca che circonda il lato sud del "
"pendio."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "Modifica da interfaccia"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "Modifica '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "Modifica l'intestazione della pagina 'Tutti i blog'."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "Modifica l'intestazione della pagina 'Risultati Filtro'."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Edit your title, the subtitle is optional."
msgstr "Modifica il titolo, il sottotitolo è facoltativo."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Enter your post's title"
msgstr "Inserisci il titolo del tuo post"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "Cose da tenere a mente"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"Infine, il team di ispettori della qualità dovrebbe aver visitato "
"regolarmente l'hotel in questione, incontrato il personale, dormito in una "
"camera e provato il cibo. Dovrebbero vivere l'esperienza dell'hotel come "
"solo un ospite può fare e a quel punto saranno davvero in grado di scrivere "
"dell'hotel."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Follow Us"
msgstr "Seguici"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Follow us"
msgstr "Seguici"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"Per molti di noi, abitanti della città, non è un'abitudine guardare il cielo"
" in maniera regolare. Le luci della città nascondono il cielo sulle nostre "
"teste in qualsiasi momento."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"Per molti di noi, la nostra prima esperienza con i corpi celesti è stata "
"vedere la nostra prima luna piena nel cielo. È davvero una vista magnifica "
"anche ad occhio nudo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"Dal bambino più piccolo all'astrofisico più avanzato, c'è qualcosa per "
"chiunque voglia godersi l'astronomia. In effetti, è una scienza così "
"accessibile che praticamente chiunque può praticarla ovunque si trovi. "
"Bisogna solo saper guardare in alto."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Full-Width"
msgstr "Intera larghezza"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "Diventa un fanatico della tecnologia"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "Compra un telescopio"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "Goditi un po' di storia"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "Esordiente"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Grid"
msgstr "Griglia"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "Ecco alcuni punti chiave che dovreste tenere a mente:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "Idee vacanza"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Hover effect"
msgstr "Effetto al passaggio"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "Come guardare in alto"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"Quanto è complesso il telescopio e avrà problemi di manutenzione? Mettiti in"
" contatto con altri per ottenere le risposte a queste e altre domande. Se "
"fai i compiti a casa in questo modo, troverai il telescopio giusto per "
"questo prossimo grande passo nell'evoluzione della tua passione per "
"l'astronomia."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "Quanto deve essere mobile il tuo telescopio?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
msgid "How to choose the right hotel"
msgstr "Come scegliere il giusto hotel"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"Ad esempio, se per te è importante che l'hotel si trovi in spiaggia, vicino "
"ad un parco tematico o vicino ad un aeroporto allora la posizione è "
"fondamentale. Un catalogo che si rispetti dovrebbe offrire una mappa degli "
"hotel e dei dintorni. Inoltre, dovrebbero esserci i grafici delle distanze "
"dall'aeroporto e una sorta di mappa interattiva."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"àSe la notte è serena, puoi osservare fantastici dettagli della superficie lunare semplicemente guardando le stelle nel tuo giardino.\n"
"Ovviamente, con la crescita del tuo amore per l'astrologia, scoprirari molti corpi celesti affascinanti. La luna forse resterà il nostro primo amore perché è l'unico oggetto lontano nello spazio che viaggia vicino alla Terra e sulla cui superficie l'uomo ha camminato. "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
msgid "In"
msgstr "Entro"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"Da più punti di vista, è possibile considerarlo un passo importante sia per "
"qualcuno che ama l'astronomia che per uno studente davvero interessato alla "
"scienza. Tuttavia, entrambi sappiamo che c'è un altro passo da compiere dopo"
" aver comprato un telescopio prima di sapere davvero come utilizzarlo."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Increase Readability"
msgstr "Aumenta leggibilità"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
msgid "Is Published"
msgstr "È pubblicato"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Islands"
msgstr "Isole"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"È molto divertente iniziare a imparare le costellazioni, come navigare nel "
"cielo notturno e trovare i pianeti e le stelle famose. Ci sono siti web e "
"libri che ti guidano."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"È importante scegliere un hotel che ti faccia sentire a casa - arredamento "
"contemporaneo o tradizionale, decorazioni locali o internazionali, formale o"
" meno. L'elenco degli hotel ideale dovrebbe metterti a conoscenza delle "
"opzioni disponibili."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"Si può dire che a un certo punto della nostra vita, ognuno di noi ha quel "
"momento in cui siamo improvvisamente storditi quando ci troviamo faccia a "
"faccia con l'enormità dell'universo che vediamo nel cielo notturno."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"È davvero incredibile il fatto che, alzando lo sguardo in una notte "
"qualsiasi, si possono vedere virtualmente centinaia di migliaia di stelle, "
"sistemi stellari, pianeti, lune, asteroidi, comete e forse anche una navetta"
" spaziale che passa di tanto in tanto. La cosa è ancora più strabiliante "
"quando ci si rende conto che il cielo che si sta guardando è a tutti gli "
"effetti lo stesso cielo di cui godevano i nostri antenati centinaia e "
"migliaia di anni fa quando alzavano semplicemente lo sguardo."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Jungle"
msgstr "Giungla"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "Sapere cosa si sta guardando"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "Sapere quando guardare"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Large"
msgstr "Grande"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Ultimo contributo di"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Latest"
msgstr "Più recente"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "Ultimi post del blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Layout"
msgstr "Struttura"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"Studiare i retroscena delle grandi scoperte dell'astronomia renderà più "
"significativi i tuoi momenti di osservazione delle stelle. Si tratta di una "
"delle scienze più antiche della Terra, quindi scopri i grandi della storia "
"che hanno osservato le stelle prima di te."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "Strutture ricreative"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "List"
msgstr "elenco"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"Le tradizioni locali sono importanti ma anche i ristoranti e i bar possono "
"giocare un ruolo importante durante la tua permanenza. Dovresti essere "
"consapevole della scelta, conoscere lo stile e il livello di formalità. Un "
"buon resoconto dell'hotel dovrebbe darti tutte queste informazioni, in "
"particolare sul servizio per la prima colazione."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "Posizione"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Marley"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Maui helicopter tours"
msgstr "Giro in elicottero a Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"I tour in elicottero sull'isola di Maui ti permettono di vederla da un'altra"
" prospettiva e vivere un'avventura. Se non sei mai salito su un elicottero, "
"questa è l'occasione buona per farlo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"I tour in elicottero sono un bel modo di visitare posti che non possono "
"essere raggiunti a piedi o in auto. I tour durano all'incirca un'ora ed "
"hanno un costo che va da circa 185 a 240 dollari a persona. Per molti, si "
"tratta di un'opportunità che capita una volta nella vita. Non dimenticare "
"macchinette fotografiche e videocamere per catturare i momenti che ti "
"permetteranno di rivivere il tour tutte le volte che vorrai e mantenere vivo"
" il ricordo negli anni."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"I tour in elicottero di Maui ti permetteranno di vedere tutte queste "
"attrazioni. Assicurati di prendere una videocamera con te per catturare la "
"bellezza del paesaggio e per mostrare ad amici e parenti a casa tutte le "
"cose meravigliose che hai visto durante la vacanza."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Medium"
msgstr "Media"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "Meta descrizione"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "Meta parole chiave"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "Meta titolo"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "Molokai e Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"I tour in elicottero di Molokai ti faranno scoprire un'altra isola a soli nove miglia di distanza e facilmente accessibile in aereo. L'isola ha una popolazione molto ridotta, appartenente a varie culture. L'intera costa nord-orientale è contornata da scogliere e spiagge remote. Sono completamente inaccessibili con qualsiasi altro mezzo di trasporto che non sia l'aereo.\n"
"Le persone che vivono sull'isola non hanno mai visto questo straordinario scenario, a meno che non abbiano fatto dei tour in elicottero di Maui per vederlo. Quando il tempo è piovoso e le precipitazioni sono abbondanti per la stagione, si possono vedere molte cascate stupefacenti."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"Per le famiglie è molto importante capire se l'hotel scelto dispone di "
"servizi per bambini. In particolare, è bene controllare se l'hotel offre il "
"servizio di baby sitting. Per coloro che viaggiano per affari e vogliono "
"evitare la confusione forse un hotel che non accoglie famiglie potrebbe "
"essere più adatto!"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "I post più visti del blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Nome"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Name / Latest Post"
msgstr "Nome/ultimo post"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.blog_post_action_add
msgid "New Blog Post"
msgstr "Nuovo articolo del blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Next Article"
msgstr "Prossimo articolo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "No Cover"
msgstr "Nessuna copertina"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "Ancora nessun articolo nel blog."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "N. di visualizzazioni"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "Nessun risultato per \"%s\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "Nessun risultato trovato per '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "Ancora nessuna etichetta definita."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "Nessuno"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"Tutto questo non ti impedisce di andare avanti con i tuoi piani e assemblare"
" un fantastico sistema di telescopi. Assicurati soltanto di ottenere "
"informazioni  e insegnamenti di qualità su come configurare il tuo "
"telescopio per rispondere ai tuoi bisogni. Utilizzando queste linee guida, "
"potrai godere di ore di divertimento nell'osservazione dello straordibnario "
"cielo notturno che vanno oltre l'occhio nudo. "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Not Published"
msgstr "Non pubblicato"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"Non è sufficiente conoscere il meteo per osservare le stelle ma sapere "
"quando si verificheranno sciami meteorici e altri eventi astronomici ti "
"permettera di vivere l'emozione dell'astronomia."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"Naturalmente, per coltivare al massimo lo studio, procurarti un buon "
"telescopio di base ti permetterà di ottenere i dettagli più sorprendenti "
"della superficie lunare. Con ognuno di questi aggiornamenti, le tue "
"conoscenze, la profondità e la portata di ciò che sarai in grado di vedere "
"miglioreranno geometricamente. Per molti astronomi dilettanti, a volte non è"
" mai abbastanza quello che possiamo vedere grazie al nostro oggetto più "
"vicino allo spazio."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/ir_qweb_fields.py:0
#, python-format
msgid ""
"On your default language, empty the blog post description and save to get an"
" automated (translated) summary."
msgstr ""
"Dalla lingua predefinita, svuota la descrizione dell'articolo del blog e "
"salva per ottenere un riassunto automatico (tradotto)."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"Once you have reviewed the content on mobile, you can switch back to the "
"normal view by clicking here again"
msgstr ""
"Una volta che hai revisionato il conenuto sul telefono, puoi tornare alla "
"visualizzazione classica cliccando di nuovo qui"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "Altri"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "I nostri blog"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "Foto di Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "Foto di Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "Foto di Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "Foto di Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "Foto di Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "Foto di Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "Foto di Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "Foto di Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "Foto di PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "Foto di SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "Foto di Teddy Kelley, @teddykelley"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "Articoli"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Posts List"
msgstr "Elenco post"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Data di pubblicazione"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Published"
msgstr "Pubblicato"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "Pubblicato ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "Data di pubblicazione"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Articolo pubblicato"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "Opzioni di pubblicazione"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "Data pubblicazione"

#. module: website_blog
#: model:ir.model,name:website_blog.model_ir_qweb_field
msgid "Qweb Field"
msgstr "Campo QWeb"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__rating_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__rating_ids
msgid "Ratings"
msgstr "Valutazioni"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Read more"
msgstr "Leggi di più"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"oi oi-chevron-right ms-2\"/>"
msgstr "Leggi di più <i class=\"oi oi-chevron-right ms-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "Ristoranti, bar e pub"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict publishing to this website."
msgstr "Limita la pubblicazione a questo sito web."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "Ottimizzato SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "Campione"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Satellites"
msgstr "Satelliti"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Search for an image. (eg: type \"business\")"
msgstr "Cerca un'immagine. (es. digita \"affari\")"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seaside vs mountain side"
msgstr "Mare vs. Montagna"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seeing the world from above"
msgstr "Vedere il mondo dall'alto"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
msgid "Select Blog"
msgstr "Seleziona un blog"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select the blog you want to add the post to."
msgstr "Seleziona il blog al quale vuoi aggiungere l'articolo."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr ""
"Seleziona questa voce di menù per creare un nuovo articolo per il blog."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Comment"
msgstr "Seleziona per commentare"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Tweet"
msgstr "Seleziona per twittare"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "Nome SEO"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "Separa ogni parola chiave con una virgola"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Set a blog post <b>cover</b>."
msgstr "Imposta una <b>copertina</b> per l'articolo del blog."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"Diversi uccelli migratori e nativi, mammiferi e rettili abitano Copper "
"Canyon. Anche la splendida fauna di questa terra quasi incontaminata merita "
"di essere visitata."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Share Links"
msgstr "Condividere link"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Condividi su Facebook"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "Condividi su Linkedln"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Twitter"
msgstr "Condividi su Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "Condividi articolo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Sidebar"
msgstr "Barra laterale"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
msgid "Sierra Tarahumara"
msgstr "Sierra Tarahumara"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"La Sierra Tarahumara, comunemente conosciuta come Copper Canyon, si trova in"
" Messico. La zona è una delle destinazioni preferite tra coloro che cercano "
"una vacanza avventurosa."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Skies"
msgstr "Cieli"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"Quindi, è di vitale importanza acquistare il telescopio giusto per il posto "
"in cui ti trovi e le tue preferenze in materia di osservazione delle stelle."
" Per cominciare, parliamo dei tre tipi principali di telescopio per poi "
"affrontare alcuni concetti introduttivi sul mondo dei telescopi per "
"aumentare la possibilità di acquistare quello giusto."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"Può capitare che una volta all'anno, durante una vacanza in campeggio o una "
"gita a casa di un parente in campagna, ci si ritrovi all'aperto quando il "
"cielo notturno decide improvvisamente di mettere in scena il suo spettacolo."
" Se ti è capitato di vivere quel tipo di momento in cui sei rimasto "
"letteralmente senza fiato di fronte allo spettacolo che il cielo notturno è "
"in grado di mostrarci, probabilmente ricorderai quel momento esatto in cui "
"non hai potuto dire altro che \"wow\"."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"Per selezionare il tipo giusto di telescopio è importante capire quali sono "
"i tuoi scopi di utilizzo. Per capire davvero i punti di forza e debolezza "
"non solo delle lenti e della struttura dello strumento ma anche le "
"prestazioni del telescopio stesso in varie situazioni di osservazione delle "
"stelle, è meglio fare un po' di compiti a casa prima e conoscere i diversi "
"tipi di telescopio. Quindi, prima di procedere con l'acquisto..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr ""
"Quindi stai andando all'estero, hai scelto la tua destinazione e ora devi "
"scegliere un hotel."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "Qualcuno noto in <cite title=\"Titolo fonte\">«Titolo fonte»</cite>"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Spotting the fauna"
msgstr "Avvistamento della fauna"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
#, python-format
msgid "Start writing here..."
msgstr "Inizia a scrivere qui..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "Stile"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "Sottotitolo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "Sottotitolo"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "Categorie etichetta"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "Categoria etichetta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "Modulo categoria etichetta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Modulo etichetta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Elenco etichette"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists!"
msgstr "La categoria tag esiste già!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Nome etichetta già esistente."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "Etichette"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags List"
msgstr "Elenco tag"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Taking pictures in the dark"
msgstr "Scattare foto al buio"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
msgid "Teaser"
msgstr "Teaser"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Teaser & Tags"
msgstr "Teaser e tag"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "Contenuto del teaser"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"Dieci anni fa, probabilmente avresti dovuto recarti nell'agenzia di viaggi "
"più vicina a te e fidarti dell'opinione dei così detti \"esperti\". Nel "
"ventunesimo secolo, puoi selezionare e prenotare il tuo hotel su Internet "
"tramite siti web di viaggio."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"Quel \"wow\" corrisponde a tutto ciò che l'astrologia è. Per alcuni, quel "
"momento wow diventa una passione che porta ad una carriera improntata sullo "
"studio delle stelle. Per alcuni fortunati, quel momento di meraviglia "
"diventa un'ossessione che li porta a viaggiare verso le stelle con lo "
"shuttle o in una delle prime missioni spaziali. Tuttavia, er la maggior "
"parte di noi l'astrologia può diventare un passatempo o un hobby regolare. "
"Quel momento di meraviglia lo portiamo con noi per il resto della vita e "
"iniziamo a cercare modi per guardare più a fondo e imparare di più sullo "
"spettacolare universo che vediamo nei milioni di stelle sopra di noi, ogni "
"notte."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "Il bello dell'astronomia è che chiunque può praticarla."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"Il miglior momento per vedere la luna, ovviamente, è durante la notte quando"
" ci sono poche nuvole e il meteo permette di poterla studiare a lungo. Il "
"primo quarto è quello che offre la maggior parte dei dettagli da studiare. "
"Non lasciaterti ingannare dall'oscuramento di una parte quando non è in fase"
" di luna piena. Il fenomeno noto come \"earthshine\" consente di vedere "
"anche la parte oscurata della luna con un certo dettaglio, anche se la luna "
"è solo a un quarto o a metà della sua esposizione."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "Il momento migliore per guardare la luna."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"Se impostato a pubblicato, l'articolo del blog viene reso visibile ai "
"visitatori a partire da questa data."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"Le scogliere sono tra le più alte nel mondo e vedere cascate d'acqua "
"altissime è qualcosa di incredibile. La breve gita da Maui in elicottero è "
"un'esperienza che vale la pena vivere per ammirare la bellezza della natura."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "URL completo per accedere al documento dal sito web."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"La prossima cosa che bisogna procurarsi è ovviamente un buon telescopio. "
"Avrai forse già visto un amatore che è già a buon punto nel suo studio e che"
" sta montando quei telescopi davvero belli su una collina da qualche parte. "
"L'atronomo principiante che è in te sarà entusiasta perché si tratta del "
"prossimo step che ti ritroverai ad affrontare. Tuttavia, comprare un buon "
"telescopio può davvero confonderti e metterti paura."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"Il posto dovrebbe offrire una descrizione dettagliata dei servizi offerti "
"dall'hotel come spa, piscina, palestra e sauna così come dettagli su "
"qualsiasi altra struttura nei paraggi come campi da golf. 7. Bisogni "
"speciali: l'hotel dovrebbe anche mettere al corrente i visitatori di ogni "
"servizio speciale così come della politica di accessibilità. È di vitale "
"importanza avere tali informazioni anche se non riguardano tutti i "
"visitatori."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"Le decisioni relative al treppiede o ad altri accessori cambieranno "
"significativamente con un telescopio che rimarrà sul tuo ponte rispetto a "
"uno che hai intenzione di portare in diversi luoghi."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"La vista è davvero mozzafiato, da non perdere. Inoltre, si tratta di "
"un'attività educativa che permette di vedere un vulcano inattivo da vicino, "
"qualcosa che non accade ogni giorno. Sul lato settentrionale e su quello "
"meridionale, tuttavia, si può osservare un panorama incredibilmente diverso."
" Questi lati sono rigogliosi e verdeggianti e potrai ammirare bellissime "
"cascate e splendidi cespugli. Le foreste pluviali tropicali abbondano su "
"questo lato dell'isola e non sono facilmente accessibili con altri mezzi se "
"non in aereo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"Poi c'è il problema della motivazione. Più recensioni leggi, più ti accorgi "
"come tendano a raggrupparsi agli estremi. Da un lato, ci sono recensori "
"arrabbiati e dall'altro, ci sono ospiti deliziati che fanno elogi oltre ogni"
" aspettativa. Non ti sorprenderà sapere che a volte gli hotel pubblicano le "
"loro stesse recensioni entusiastiche o che i rivali fanno la fila per avere "
"la possibilità di criticare la concorrenza con recensioni negative. È "
"opportuno considerare ciò che è veramente importante quando scegli un hotel."
" Dovresti quindi scegliere un elenco di hotel online che fornisca "
"informazioni aggiornate, indipendenti e imparziali che contano davvero."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr ""
"Ci sono altre considerazioni da fare nella vostra decisione finale di "
"acquisto."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"Il cosmo è qualcosa di eterno. Il fatto che i pianeti, la luna e le stelle "
"siano state lì per secoli, ci fa capire qual è il nostro posto "
"nell'universo. Infatti, molte delle stelle che \"vediamo\" a occhio nudo "
"sono in realtà luce proveniente da quella stella centinaia di migliaia di "
"anni fa. Quella luce sta raggiungendo la Terra solo ora. Quindi, in un modo "
"molto reale, guardare in alto è come viaggiare nel tempo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"Tutto questo è davvero importante e qualsiasi elenco di hotel dovrebbe "
"fornirti tali informazioni sulle camere, non solo il numero di camere, "
"opzione abituale!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "Questa casella non sarà visibile ai tuoi visitatori"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "This tag already exists"
msgstr "Etichetta già esistente"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Tiny"
msgstr "Minuscolo"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "Titolo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Above Cover"
msgstr "Titolo sopra la copertina"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Inside Cover"
msgstr "Titolo nella copertina"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"Per guardare la luna a occhio nudo, familiarizzare con la mappa lunare ti "
"aiuterà a identificare mari, crateri e altri fenomeni geografici che altri "
"hanno già mappato per rendere lo studio più piacevole. Le mappe della luna "
"possono essere acquistate in qualsiasi negozio di astronomia o onlline e "
"vale la pena acquistarle."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"Per iniziare a imparare come osservare le stelle ci sono alcune cose base da"
" approfondire oltre a ciò che possiamo vedere ad occhio nudo e iniziare a "
"studiare e apprezzare le stelle. La prima cosa di cui hai bisogno non è un "
"impianto ma della letteratura. Una buona mappa delle stelle ti mostrerà gran"
" parte delle costellazioni, la posizione delle stelle principali che "
"utilizziamo per navigare nelc ielo e i pianeti che appaiono più grandi delle"
" stelle. Se aggiungi del materiale introduttivo ben scritto al tuo hobby, "
"sei sulla buona strada."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"Per dare un tocco in più, un buon binocolo può fare miracoli per i dettagli "
"che vedrai sulla superficie lunare. Per ottenere migliori risultati, è "
"consigliabile un buon campo largo nelle impostazioni del binocolo, in modo "
"da poter ammirare il paesaggio lunare in tutta la sua bellezza. Poiché è "
"quasi impossibile tenere fermo il binocolo per tutto il tempo che vorrai "
"dedicare a questo magnifico corpo celeste, potrai aggiungere al tuo arsenale"
" un buon treppiede su cui fissare il binocolo, in modo da poter studiare la "
"Luna in tutta comodità e con una piattaforma di osservazione stabile."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"Per passare a un livello superiore, potresti approfittare di collaborazioni "
"con altri astronomi o visitare uno dei grandiosi telescopi che sono stati "
"allestiti da professionisti che hanno investito in tecniche migliori per "
"eliminare le interferenze atmosferiche, per vedere la luna ancora meglio. "
"Internet permette di accedere all'Hubble e a molti altri telescopi enormi "
"che sono costantemente puntati sulla Luna. Inoltre, molti club di astronomia"
" stanno studiando modi per combinare più telescopi, accuratamente "
"sincronizzati con i computer al fine di ottenere la migliore visione del "
"paesaggio lunare."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Top Banner"
msgstr "Intestazione superiore"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "Viaggi"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "Non pubblicato ("

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "Articolo senza titolo"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"Usa questa icona per l'anteprima dell'articolo sui <b>dispositivi "
"mobili</b>."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Usato in:"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Viewpoints"
msgstr "Punti di vista"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "visualizzazioni"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "Visibile in tutte le pagine del blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "Visibile nel sito web corrente"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr "SCRIVI QUI O TRASCINA I BLOCCHI"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Sito web"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Blog del sito web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtro snippet sito web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "URL sito web"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Meta descrizione sito web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta parole chiave sito web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Meta titolo sito web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Immagine Open Graph sito web"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "E se ti lasciassero guidare l'Hubble?"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"Chiunque può guardare il cielo e innamorarsi delle stelle, in qualsiasi "
"momento, ma il divertimento dell'astronomia consiste nell'imparare come "
"diventare sempre più abile nell'osservazione delle stelle e capirne sempre "
"di più. Di seguito alcuni passi da seguire per rendere i momenti che dedichi"
" all'astronomia ancora più piacevoli."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "With a View"
msgstr "Con vista"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr ""
"Scrivi qui un piccolo testo per descrivere il tuo blog o la tua azienda."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"Dovresti sempre considerare attentamente il tipo di servizi di cui hai "
"bisogno per il tuo alloggio e trovare l'hotel che fornisca quelli che "
"ritieni importanti. Il sito web dell'hotel dovrebbe fornire informazioni su:"
" dimensioni del letto, connessione internet (costi, se è presente il WiFi o "
"una connessione cablata a banda larga), servizi gratuiti, vista dalla camera"
" e servizi di lusso come il menu dei cuscini o dei bagni, presenza di camere"
" fumatori o meno e altro."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"Vedrai quanta bellezza ha da offrire Maui e trascorrerai una bella giornata "
"con la tua famiglia. I tour non sono costosi e durano da quarantacinque "
"minuti a un'ora. Potrai vedere posti che sono tipicamente inaccessibili, non"
" raggiungibili a piedi o con i mezzi ma visibili dall'alto. Ti aspettano "
"delle viste mozzafiato e se rimarrai sull'isola per molto tempo puoi anche "
"valutare di fare più tour."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "avventura"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr ". Fai clic per accedere :"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "percorso di navigazione"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "di"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "scoperta"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "guide"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "sul blog"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "hotel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "in"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "in <i class=\"fa fa-folder-open text-muted\"/>"
msgstr "in <i class=\"fa fa-folder-open text-muted\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "in <i class=\"fa fa-folder-open text-white-75\"/>"
msgstr "in <i class=\"fa fa-folder-open text-white-75\"/>"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "telescopi"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "per lasciare un commento"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "non pubblicate"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"
msgstr ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| Ancora nessun commento"
