.o-spreadsheet {
    height: 100%;

    .fa {
        font-family: FontAwesome;
    }

    .o-selection input {
        display: initial;
    }
}

.o-sidePanel {
    .o-input{
        background-origin: content-box;
    }

    .o-sidePanelButtons .o-button {
        color: #666;

        &.o_global_filter_save {
            color: $o-brand-primary;
            border-color: $o-brand-primary;
        }

        &.o_delete_element {
            color: #fff;
            background-color: map-get($theme-colors, "danger");
            border-color: map-get($theme-colors, "danger");

            &:hover:enabled {
                background-color: darken(map-get($theme-colors, "danger"), 7%);
                border-color: #bd2130;
            }
        }
    }
}
