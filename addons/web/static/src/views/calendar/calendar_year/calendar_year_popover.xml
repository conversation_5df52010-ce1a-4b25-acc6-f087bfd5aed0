<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.CalendarYearPopover">
        <t t-if="env.isSmall">
            <Dialog title="dialogTitle">
                <t t-call="{{ constructor.subTemplates.body }}" />
                <t t-set-slot="footer">
                    <t t-call="{{ constructor.subTemplates.footer }}" />
                </t>
            </Dialog>
        </t>
        <t t-else="">
            <t t-call="{{ constructor.subTemplates.popover }}" />
        </t>
    </t>

    <t t-name="web.CalendarYearPopover.popover">
        <div class="position-absolute" style="top: .75rem; right: 1rem;">
            <span class="o_cw_popover_close" t-on-click.stop="() => props.close()">
                <i class="fc-close fc-icon fc-icon-x" />
            </span>
        </div>
        <div class="o_cw_body popover-body d-flex flex-column gap-2">
            <t t-call="{{ constructor.subTemplates.body }}" />
            <t t-if="props.model.canCreate">
                <t t-call="{{ constructor.subTemplates.footer }}" />
            </t>
        </div>
    </t>

    <t t-name="web.CalendarYearPopover.body">
        <div t-foreach="recordGroups" t-as="recordGroup" t-key="recordGroup.title">
            <h5 t-out="recordGroup.title" />
            <t t-foreach="recordGroup.records" t-as="record" t-key="record.id">
                <t t-call="{{ constructor.subTemplates.record }}" />
            </t>
        </div>
    </t>

    <t t-name="web.CalendarYearPopover.footer">
        <t t-if="props.model.canCreate">
            <a href="#" class="btn-link o_cw_popover_create" t-on-click.prevent="onCreateButtonClick">
                <i class="fa fa-plus" />
                Create
            </a>
        </t>
        <t t-else="">
            <button class="btn btn-primary o-default-button" t-on-click="props.close">
                <t>Ok</t>
            </button>
        </t>
    </t>

    <t t-name="web.CalendarYearPopover.record">
        <a
            href="#"
            t-on-click.prevent="() => this.onRecordClick(record)"
            t-attf-style="{{ getRecordStyle(record) }}"
            t-attf-class="o_cw_popover_link d-flex align-items-center gap-1 {{ getRecordClass(record) }} {{record.startHour ? 'o_event_dot' : 'o_event_allday'}}"
            t-att-data-id="record.id"
            t-att-data-title="record.title"
        >
            <span t-if="record.startHour"><t t-out="record.startHour" /></span>
            <span t-esc="record.title"/>
        </a>
    </t>

</templates>
