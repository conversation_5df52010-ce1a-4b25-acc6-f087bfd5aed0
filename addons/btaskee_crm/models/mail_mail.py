# -*- coding: utf-8 -*-
from odoo import models, fields, api,_, tools
import json
import requests
from odoo.exceptions import ValidationError, UserError
from datetime import datetime
import logging


_logger = logging.getLogger(__name__)

class MailMail(models.Model):

    _inherit = 'mail.mail'

    def create(self,vals):
        context = self.env.context
        if context.get('send_mail_acc'):

            email_to = vals.get('email_to')
            to_email = self.sudo().env.ref('btaskee_crm.default_email_accouting_cc').value
            if email_to:
                email_to += ',' + to_email
            else:
                email_to = to_email
            vals.update({'email_to':email_to})
        res = super().create(vals)
        return res