<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- CRM Lead Reject Wizard Form View -->
    <record id="crm_lead_reject_wizard_form" model="ir.ui.view">
        <field name="name">crm.lead.reject.wizard.form</field>
        <field name="model">crm.lead.reject.wizard</field>
        <field name="arch" type="xml">
            <form string="Reject Lead">
                <div class="alert alert-warning" role="alert">
                    <strong>Warning:</strong> You are about to reject this lead. Please provide a reason for rejection.
                </div>
                <group>
                    <field name="lead_ids" invisible="1"/>
                    <field name="rejection_reason"
                           placeholder="Please enter the reason for rejecting this lead..."
                           class="w-100"
                           widget="text"/>
                </group>
                <group string="Notification Settings">
                    <field name="send_email_notification"/>
                    <field name="notification_info" readonly="1" nolabel="1"
                           invisible="not notification_info"/>
                </group>
                <footer>
                    <button string="Reject" 
                            name="action_reject_lead" 
                            type="object" 
                            class="btn-primary" 
                            data-hotkey="q"/>
                    <button string="Cancel" 
                            class="btn-secondary" 
                            special="cancel" 
                            data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action for the wizard -->
    <record id="crm_lead_reject_wizard_action" model="ir.actions.act_window">
        <field name="name">Reject Lead</field>
        <field name="res_model">crm.lead.reject.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="crm_lead_reject_wizard_form"/>
        <field name="target">new</field>
    </record>
</odoo>
