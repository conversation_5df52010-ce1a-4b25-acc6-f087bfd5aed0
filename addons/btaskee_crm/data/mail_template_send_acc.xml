<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="email_template_crm_confirm_quotation_accounting" model="mail.template">
            <field name="name">Send Confirm Quatation to ACC</field>
            <field name="model_id" ref="model_crm_lead"/>
            <field name="subject">Team B2B - <PERSON>h toán cho hợp đồng {{ object.quotation_code or 'n/a' }}</field>
<!--            <field name="email_from">{{ (object.user_id.email_formatted or object.company_id.email_formatted or user.email_formatted) }}</field>-->
            <field name="description">Used by salespeople send to Accounting confirm</field>
            <field name="email_cc">{{object.email_cc}}</field>
            <field name="email_to">{{object.email_cc}}</field>
            <field name="body_html" type="html">
                <link rel="stylesheet" href="/btaskee_crm/static/src/css/report_styles.css"/>
                <div style="margin: 0px; padding: 0px;font-family: Times New Roman !important;">
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            line-height: 1.6;
                        }

                        .info-section {
                            margin-bottom: 20px;
                            padding: 10px;
                            border: 1px solid #bdc3c7;
                            border-radius: 5px;
                            background-color: #ecf0f1;
                        }
                        button {
                            background-color: #3498db;
                            color: white;
                            border: none;
                            padding: 10px 15px;
                            border-radius: 5px;
                            cursor: pointer;
                        }
                        button:hover {
                            background-color: #2980b9;
                        }
                    </style>


                    <h2>Dear Bộ phận Kế toán,</h2>
                    <p>BD gửi thông tin KH thanh toán như bên dưới, nhờ Bộ phận Kế toán tiến hành top up và xuất hoá đơn cho KH nhé:</p>

                    <div class="info-section">
                        <h3>Thông tin top up:</h3>
                        <ul>
                            <li>Tên DN: <span t-esc="object.partner_id.name or ''"/></li>
                            <li>SĐT: <span t-esc="object.account_phone or ''"/></li>
                            <li>Lý do: Nạp bPay cho đơn hàng số <span t-esc="object.quotation_code or ''"/></li>
                            <li>Số tiền chưa VAT:   <t t-esc="object.total_price"
                                                   t-options="{'widget': 'monetary', 'display_currency': object.company_id.currency_id}"/></li>
                            <li>Số tiền gồm VAT: <t t-esc="object.total_price * 1.1"
                                                   t-options="{'widget': 'monetary', 'display_currency': object.company_id.currency_id}"/> </li>
                            <li>Dịch vụ: <span t-esc="object.service_name or ''"/></li>
                        </ul>
                    </div>

                    <div class="info-section">
                        <h3>Thông tin xuất hoá đơn:</h3>
                        <ul>
                            <li>Mã số thuế:  <span t-esc="object.partner_id.vat or ''"/></li>
                            <li>Tên Công ty: <span t-esc="object.invoice_address_id.name or ''"/></li>
                            <li>Địa chỉ xuất hoá đơn: <span t-esc="object.invoice_address_id.shipping_address or ''"/></li>
                            <li>Email nhận hoá đơn:  <span t-esc="object.account_email or ''"/></li>
                            <li>Nội dung dịch vụ xuất hoá đơn:
                                <ul>
                                    <li t-if="object.category_id.name == 'bOffice'">Dịch vụ trong nhóm bOffice: Dịch vụ dọn dẹp vệ sinh văn phòng theo đơn hàng số <span t-esc="object.quotation_code or ''"/></li>
                                    <li t-if="object.category_id.name == 'bFlexible'">Dịch vụ trong nhóm bFlexible: Dịch vụ dọn dẹp vệ sinh linh hoạt theo đơn hàng số <span t-esc="object.quotation_code or ''"/></li>
                                    <li t-if="object.category_id.name == 'bChain'">Dịch vụ trong nhóm bChain: Dịch vụ dọn dẹp vệ sinh chuyên nghiệp dành cho Chuỗi theo đơn hàng số <span t-esc="object.quotation_code or ''"/></li>
                                    <li t-if="object.category_id.name == 'bBenefit'">Dịch vụ trong nhóm bBenefit: Dịch vụ phúc lợi chăm sóc gia đình cho nhân viên theo đơn hàng số <span t-esc="object.quotation_code or ''"/></li>
                                    <li t-if="object.category_id.name == 'bSolution'">Dịch vụ trong nhóm bSolution: Dịch vụ giải pháp trọn gói cho doanh nghiệp theo đơn hàng số <span t-esc="object.quotation_code or ''"/></li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                    <p>Cảm ơn bộ phận Kế toán,</p>
                    <p>Trân trọng,</p>
                    <p t-esc="object.user_id.name"></p>

                </div>

            </field>
            <!--            <field name="report_template_ids" eval="[(4, ref('sale.action_report_saleorder'))]"/>-->
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>


    </data>
</odoo>