<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

   <record id="ir_sequence_b_company" model="ir.sequence">
       <field name="name">Code Company Numbers</field>
       <field name="code">b.company.code</field>
       <field name="prefix">B</field>
       <field name="suffix">%(y)s</field>
       <field name="padding">5</field>
       <field name="number_next">1</field>
       <field name="number_increment">1</field>
       <field name="company_id" eval="False"/>
   </record>

    <!--CRM Stage-->
    <record model="crm.stage" id="stage_lead5">
        <field name="name">Contract sent</field>
        <field name="sequence">100</field>
        <field name="fold" eval="True"/>
    </record>
    <record model="crm.stage" id="stage_lead6">
        <field name="name">Legal</field>
        <field name="sequence">101</field>
        <field name="fold" eval="True"/>
    </record>
    <record model="crm.stage" id="stage_lead7">
        <field name="name">Payment</field>
        <field name="sequence">102</field>
        <field name="fold" eval="True"/>
    </record>

    <!--API Key to call get price from Go-->
    <record id="default_go_accesskey" model="ir.config_parameter">
        <field name="key">base.go_accesskey</field>
        <field name="value">****************************************************************</field>
    </record>

    <!--Service bTaskee-->
    <record id="air_conditioner_service" model="btaskee.service">
        <field name="name">Vệ sinh máy lạnh</field>
            <field name="en_name">(Cleaning Air Conditioner)</field>
        <field name="service_type">Air Conditioner</field>
        <field name="service_id">3T8NhtMJo8mkARqLH</field>
        <field name="api_call">http://*************/api/v5/pricing-vn/air-conditioner-v2</field>
    </record>

        <record id="office_cleaning_service" model="btaskee.service">
        <field name="service_type">Office Cleaning</field>
        <field name="name">bOffice – Dọn dẹp văn phòng căn bản</field>
            <field name="en_name">(bOffice – Office Care Basic)</field>

        <field name="service_id">x6291a09d6b0d9e0db9067735bgNWb</field>
        <field name="api_call">http://*************/api/v5/pricing-vn/subscription-office-cleaning</field>
    </record>

    <record id="flexible_cleaning_service" model="btaskee.service">
        <field name="service_type">Flexible Cleaning</field>
        <field name="name">Gói dọn dẹp vệ sinh linh hoạt</field>
        <field name="en_name">(Flexible cleaning packages)</field>
    </record>

<!--    <record id="flexible_cleaning_service" model="btaskee.service">-->
<!--        <field name="service_type">Flexible Cleaning</field>-->
<!--        <field name="name">Gói dọn dẹp vệ sinh linh hoạt</field>-->
<!--            <field name="en_name">(Flexible cleaning packages)</field>-->
<!--    </record>-->


<!--    <record id="office_cleaning_monthly_service" model="btaskee.service">-->
<!--        <field name="service_type">Office Cleaning Monthly</field>-->
<!--        <field name="service_id">x6291a09d6b0d9e0db9067735bgNWb</field>-->
<!--        <field name="api_call">http://*************/api/v5/pricing-vn/subscription-office-cleaning</field>-->
<!--    </record>-->

    <!--Air Conditioner Service Type-->
    <record model="aircon.type" id="aircon_type_wall">
        <field name="name">Wall</field>
        <field name="text_en">Wall</field>
        <field name="text_ko">벽걸이</field>
        <field name="text_th">ติดผนัง</field>
        <field name="text_vi">Treo tường</field>
        <field name="use_hp" eval="True"></field>
    </record>

    <record model="aircon.type" id="aircon_type_portable">
        <field name="name">Portable</field>
        <field name="text_en">Portable</field>
        <field name="text_ko">휴대용 에어 컨디셔너</field>
        <field name="text_th">เครื่องแอร์แนวตั้ง</field>
        <field name="text_vi">Tủ đứng</field>
        <field name="use_hp" eval="False"></field>
    </record>

    <record model="aircon.type" id="aircon_type_cassette">
        <field name="name">Cassette</field>
        <field name="text_en">Cassette</field>
        <field name="text_ko">천장형</field>
        <field name="text_th">สี่ทิศทาง</field>
        <field name="text_vi">Âm trần</field>
        <field name="use_hp" eval="True"></field>
    </record>

    <record model="aircon.type" id="aircon_type_ceilling">
        <field name="name">Floor/Ceiling</field>
        <field name="text_en">Floor/Ceiling</field>
        <field name="text_ko">상업용 천장형</field>
        <field name="text_th">แขวน/ตั้งพื้น</field>
        <field name="text_vi">Áp trần</field>
        <field name="use_hp" eval="True"></field>
    </record>

    <record model="aircon.type" id="aircon_type_built_in">
        <field name="text_en">Built-in</field>
        <field name="text_ko">빌트인</field>
        <field name="text_th">ฝังเพดาน</field>
        <field name="text_vi">Giấu trần</field>
        <field name="use_hp" eval="False"></field>
    </record>

    <record model="office.cleaning.area" id="office_cleaning_area1">
        <field name="area">100</field>
        <field name="duration">2</field>
        <field name="number_of_taskers">1</field>
    </record>
    <record model="office.cleaning.area" id="office_cleaning_area2">
        <field name="area">150</field>
        <field name="duration">3</field>
        <field name="number_of_taskers">1</field>
    </record>
    <record model="office.cleaning.area" id="office_cleaning_area3">
        <field name="area">200</field>
        <field name="duration">4</field>
        <field name="number_of_taskers">1</field>
    </record>
    <record model="office.cleaning.area" id="office_cleaning_area4">
        <field name="area">250</field>
        <field name="duration">5</field>
        <field name="number_of_taskers">1</field>
    </record>
    <record model="office.cleaning.area" id="office_cleaning_area5">
        <field name="area">300</field>
        <field name="duration">3</field>
        <field name="number_of_taskers">2</field>
    </record>
    <record model="office.cleaning.area" id="office_cleaning_area6">
        <field name="area">400</field>
        <field name="duration">4</field>
        <field name="number_of_taskers">2</field>
    </record>
    <record model="office.cleaning.area" id="office_cleaning_area7">
        <field name="area">500</field>
        <field name="duration">5</field>
        <field name="number_of_taskers">2</field>
    </record>
    <record model="office.cleaning.area" id="office_cleaning_area8">
        <field name="area">750</field>
        <field name="duration">5</field>
        <field name="number_of_taskers">3</field>
    </record>
    <record model="office.cleaning.area" id="office_cleaning_area9">
        <field name="area">900</field>
        <field name="duration">6</field>
        <field name="number_of_taskers">3</field>
    </record>

    <record model="days.week" id="monday">
        <field name="name">Thứ 2</field>
        <field name="en_name">Monday</field>
        <field name="number_value">0</field>
    </record>

    <record model="days.week" id="tuesday">
        <field name="name">Thứ 3</field>
        <field name="en_name">Tuesday</field>
        <field name="number_value">1</field>
    </record>

    <record model="days.week" id="wednesday">
        <field name="name">Thứ 4</field>
        <field name="en_name">Wednesday</field>
        <field name="number_value">2</field>
    </record>

    <record model="days.week" id="thursday">
        <field name="name">Thứ 5</field>
        <field name="en_name">Thursday</field>
        <field name="number_value">3</field>
    </record>

    <record model="days.week" id="friday">
        <field name="name">Thứ 6</field>
        <field name="en_name">Friday</field>
        <field name="number_value">4</field>
    </record>

    <record model="days.week" id="saturday">
        <field name="name">Thứ 7</field>
        <field name="en_name">Saturday</field>
        <field name="number_value">5</field>
    </record>

    <record model="days.week" id="sunday">
        <field name="name">Chủ nhật</field>
        <field name="en_name">Sunday</field>
        <field name="number_value">6</field>
    </record>
</odoo>