# Btaskee Service Line Documentation

## Overview

The `btaskee.service.line` model represents individual service requests within the Btaskee CRM system. It serves as a detailed breakdown of services provided to customers, with specific focus on air conditioner maintenance and repair services.

## Model Structure

### **Core Fields**

| Field | Type | Description |
|-------|------|-------------|
| `crm_lead_id` | Many2one | Link to CRM Lead |
| `btaskee_service_id` | Many2one | Btaskee Service type |
| `service_type` | Selection | Related service type (readonly) |
| `address_id` | Many2one | Service address (partner) |
| `note` | Text | Additional notes |
| `job_description` | Html | Detailed job description |
| `currency_id` | Many2one | Currency for pricing |
| `price` | Monetary | Total service price |

### **Related Fields**

| Field | Type | Description |
|-------|------|-------------|
| `air_conditoner_line_ids` | One2many | Air conditioner units |
| `ac_units_count` | Integer | Count of AC units (computed) |
| `total_quantity` | Integer | Total quantity (computed) |
| `total_gas_refill` | Integer | Total gas refill units (computed) |

### **Inheritance**

- `mail.thread` - Message tracking and communication
- `mail.activity.mixin` - Activity management

## Views Created

### **1. Tree View**
- **Purpose**: List overview of service lines
- **Features**: 
  - Color coding by service type
  - Monetary widget for price display
  - Quick overview of key information

### **2. Form View**
- **Purpose**: Detailed service line management
- **Features**:
  - Header with "Calculate Price" button
  - Statistics button for AC units count
  - Tabbed interface for different information types
  - Chatter integration for communication

#### **Form View Tabs**:
1. **Air Conditioner Details** - Inline editing of AC units
2. **Job Description** - Rich HTML editor for detailed descriptions
3. **Notes** - Simple text notes

### **3. Kanban View**
- **Purpose**: Card-based mobile-friendly view
- **Features**:
  - Service type display
  - Price highlighting
  - AC units count for air conditioner services
  - Responsive design

### **4. Search View**
- **Purpose**: Advanced filtering and searching
- **Filters**:
  - Service type filters
  - Price-based filters
  - Lead association filters
- **Group By**: Service type, Btaskee service, Lead, Address, Currency

### **5. Calendar View**
- **Purpose**: Timeline view of service lines
- **Features**:
  - Color coding by service type
  - Creation date based scheduling
  - Quick service overview

### **6. Pivot View**
- **Purpose**: Data analysis and reporting
- **Features**:
  - Service type breakdown
  - Monthly analysis
  - Price aggregation

### **7. Graph View**
- **Purpose**: Visual data representation
- **Features**:
  - Bar chart by service type
  - Price measurements
  - Trend analysis

## Key Features

### **1. Smart Auto-Filling**
- **From Lead**: Automatically fills service and address when lead is selected
- **Service Detection**: Auto-selects Btaskee service from linked lead
- **Address Mapping**: Maps partner address from lead

### **2. Price Calculation Integration**
- **API Integration**: Connects to Btaskee pricing API
- **Real-time Calculation**: Updates price based on AC specifications
- **Error Handling**: User-friendly notifications for calculation issues

### **3. Air Conditioner Management**
- **Inline Editing**: Direct AC unit management within service line
- **Automatic Counting**: Real-time count of AC units
- **Validation**: Ensures AC services have at least one unit

### **4. Communication Features**
- **Message Tracking**: Full message history
- **Activity Management**: Task and activity scheduling
- **Follower System**: Stakeholder notifications

## Business Logic

### **1. Creation Logic**
```python
# Auto-fill service from lead
if lead_provided and no_service_specified:
    service = lead.btaskee_service_id

# Auto-fill address from lead
if lead_provided and no_address_specified:
    address = lead.partner_id
```

### **2. Validation Rules**
- Air Conditioner services must have at least one AC unit
- Required fields: `btaskee_service_id`
- Currency defaults to company currency

### **3. Computed Fields**
- `ac_units_count`: Count of related AC lines
- `total_quantity`: Sum of all AC unit quantities
- `total_gas_refill`: Sum of all gas refill quantities

## Menu Structure

```
CRM > Services
├── Service Lines (All service lines)
├── AC Service Lines (Air conditioner only)
└── Air Conditioner Service
    └── AC Units (Individual AC units)
```

## Actions Available

### **1. Calculate Price**
- **Trigger**: Button in form view header
- **Function**: Calls Btaskee API for price calculation
- **Requirements**: Air Conditioner service with linked lead

### **2. View AC Lines**
- **Trigger**: Statistics button in form view
- **Function**: Opens related AC units in separate view
- **Context**: Pre-filtered to current service line

### **3. Standard CRUD Operations**
- Create, Read, Update, Delete
- Copy with price reset
- Archive/Unarchive

## Integration Points

### **1. CRM Lead Integration**
- Bidirectional relationship with leads
- Auto-population of service details
- Price synchronization

### **2. Air Conditioner Lines**
- One-to-many relationship
- Automatic indexing
- Validation dependencies

### **3. Partner/Address Management**
- Service location tracking
- Customer information linking
- Geographic data integration

## Usage Examples

### **Creating a Service Line**
1. Navigate to CRM > Services > Service Lines
2. Click "Create"
3. Select CRM Lead (auto-fills service and address)
4. Add air conditioner details in the AC Details tab
5. Click "Calculate Price" to get pricing
6. Save the record

### **Managing AC Units**
1. Open service line form
2. Go to "Air Conditioner Details" tab
3. Add/edit AC units inline
4. Specify type, HP range, quantity, and gas refill
5. System automatically updates totals

### **Price Calculation**
1. Ensure service line has linked lead
2. Add AC unit specifications
3. Click "Calculate Price" button
4. System calls Btaskee API and updates price
5. Notification shows success/error status

## Security

- **User Access**: All users can read/write service lines
- **Manager Access**: CRM managers have full access
- **Field Security**: Standard Odoo field-level security
- **Record Rules**: No special record rules (inherits from base)

## Testing

Comprehensive test suite covers:
- Basic CRUD operations
- Auto-filling functionality
- Computed field calculations
- Validation rules
- Mail thread integration
- Price calculation workflow

## API Integration

Service lines integrate with the Btaskee CRM API:
- **Endpoint**: `/api/btaskee/lead/{id}/price`
- **Method**: GET
- **Authentication**: API key required
- **Response**: Price data with error handling

## Performance Considerations

- **Computed Fields**: Stored for better performance
- **Indexing**: Automatic indexing on foreign keys
- **Caching**: Standard Odoo ORM caching
- **Pagination**: Built-in pagination for large datasets

## Customization Points

- **Additional Service Types**: Extend service type selection
- **Custom Fields**: Add business-specific fields
- **Workflow States**: Implement state management
- **Custom Validations**: Add business rule validations
- **Report Integration**: Create custom reports and dashboards
