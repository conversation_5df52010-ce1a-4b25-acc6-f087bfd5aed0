# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class CrmLeadRejectWizard(models.TransientModel):
    _name = 'crm.lead.reject.wizard'
    _description = 'CRM Lead Rejection Wizard'

    lead_ids = fields.Many2many('crm.lead', string='Leads')
    rejection_reason = fields.Text(string='Rejection Reason', required=True,
                                   help='Please provide a reason for rejecting this lead')
    send_email_notification = fields.Boolean(string='Send Email Notification', default=True,
                                            help='Send email notification to the person in charge')
    notification_info = fields.Html(string='Notification Info', compute='_compute_notification_info')

    @api.model
    def default_get(self, fields_list):
        """Set default lead_ids from context"""
        res = super().default_get(fields_list)
        if 'lead_ids' in fields_list:
            res['lead_ids'] = self.env.context.get('active_ids', [])
        return res

    @api.depends('lead_ids')
    def _compute_notification_info(self):
        """Compute notification information"""
        for wizard in self:
            info_lines = []
            for lead in wizard.lead_ids:
                if lead.user_id:
                    email_status = _('✓ Email') if lead.user_id.partner_id.email else _('✗ No Email')
                    info_lines.append(_('• %s (%s): %s') % (lead.name, lead.user_id.name, email_status))
                else:
                    info_lines.append(_('• %s: No person in charge assigned') % lead.name)

            if info_lines:
                wizard.notification_info = '<br/>'.join(info_lines)
            else:
                wizard.notification_info = _('No leads selected')

    def action_reject_lead(self):
        """Apply rejection to selected leads"""
        self.ensure_one()
        if not self.rejection_reason.strip():
            raise ValidationError(_('Rejection reason is required.'))

        # Apply rejection to all selected leads
        for lead in self.lead_ids:
            lead.action_reject(self.rejection_reason)

            # Send notification to the person in charge (user_id)
            if lead.user_id:
                self._send_rejection_notification(lead, self.send_email_notification)
            else:
                # Log that no person in charge is assigned
                lead.message_post(
                    body=_('Lead rejected by %s, but no person in charge assigned to receive notification.<br/><strong>Reason:</strong> %s') % (
                        self.env.user.name,
                        self.rejection_reason.replace('\n', '<br/>')
                    ),
                    message_type='comment',
                    subtype_xmlid='mail.mt_comment'
                )

        return {'type': 'ir.actions.act_window_close'}

    def _get_lead_url(self, lead):
        """Generate URL to view lead in Odoo"""
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url', 'http://localhost:8069')
        return f"{base_url}/web#id={lead.id}&model=crm.lead&view_type=form"

    def _send_rejection_notification(self, lead, send_email=True):
        """Send notification to the person in charge about lead rejection"""
        # Create activity for the person in charge

        # Send internal message/notification
        message_subtype = 'mail.mt_comment' if send_email else 'mail.mt_note'
        lead_url = self._get_lead_url(lead)

        lead.message_post(
            body=_('Lead rejected by %s.<br/><strong>Reason:</strong> %s<br/><br/><a href="%s" style="color: #007bff;">📋 View Lead Details</a>') % (
                self.env.user.name,
                self.rejection_reason.replace('\n', '<br/>'),
                lead_url
            ),
            message_type='notification',
            partner_ids=[lead.user_id.partner_id.id] if lead.user_id.partner_id else [],
            subtype_xmlid=message_subtype
        )

        # Send email notification if requested and user has email
        if send_email and lead.user_id.partner_id.email:
            self._send_rejection_email(lead)

    def _send_rejection_email(self, lead):
        """Send email notification about lead rejection"""
        try:
            # Create email values
            email_values = {
                'subject': _('Lead Rejected: %s') % lead.name,
                'body_html': self._get_rejection_email_body(lead),
                'email_to': lead.user_id.partner_id.email,
                'email_from': self.env.user.email or self.env.company.email,
                'reply_to': self.env.user.email or self.env.company.email,
                'auto_delete': True,
            }

            # Send email
            mail = self.env['mail.mail'].sudo().create(email_values)
            mail.send()

        except Exception as e:
            # Log error but don't fail the rejection process
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning('Failed to send rejection email for lead %s: %s', lead.name, str(e))

    def _get_rejection_email_body(self, lead):
        """Generate email body for rejection notification"""
        lead_url = self._get_lead_url(lead)

        return _("""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #d9534f;">Lead Rejected</h2>

            <p>Dear <strong>%s</strong>,</p>

            <p>Your lead <strong>"%s"</strong> has been rejected by <strong>%s</strong>.</p>

            <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #d9534f; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #d9534f;">Rejection Reason:</h4>
                <p style="margin-bottom: 0;">%s</p>
            </div>

            <p>Please review the rejection reason and take appropriate action if needed.</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="%s"
                   style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                    View Lead in Odoo
                </a>
            </div>

            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">

            <p style="font-size: 12px; color: #666;">
                This is an automated notification from the CRM system.<br/>
                Lead ID: %s<br/>
                Rejected on: %s<br/>
                <a href="%s" style="color: #007bff;">Click here to view the lead</a>
            </p>
        </div>
        """) % (
            lead.user_id.name,
            lead.name,
            self.env.user.name,
            self.rejection_reason.replace('\n', '<br/>'),
            lead_url,
            lead.id,
            fields.Datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            lead_url
        )
