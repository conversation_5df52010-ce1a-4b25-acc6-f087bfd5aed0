# Btaskee CRM API Documentation

## Overview

The Btaskee CRM API provides RESTful endpoints for managing leads, services, and air conditioner configurations. This API allows external systems to integrate with the Btaskee CRM module in Odoo.

## Base URL

```
http://your-odoo-domain.com/api/btaskee
```

## Authentication

All API endpoints (except `/health` and `/docs`) require an API key for authentication.

### API Key Methods

1. **Query Parameter**: Add `api_key` as a URL parameter
   ```
   GET /api/btaskee/leads?api_key=your_api_key
   ```

2. **JSON Body**: Include `api_key` in the request body
   ```json
   {
     "api_key": "your_api_key",
     "lead_data": {...}
   }
   ```

### Valid API Keys
- `btaskee_api_key_2024`
- `1JtpyvG4nr7gJfTkWl57JrdwN5RGuunhHGnWEpTPVofIzyqgXmPCus3IdgfpZGan`

## Response Format

All API responses follow this format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {...}
}
```

### Error Response
```json
{
  "error": true,
  "message": "Error description",
  "status": 400,
  "error_code": "ERROR_CODE"
}
```

## Endpoints

### 1. Health Check

Check API availability and status.

**Endpoint**: `GET /health`  
**Authentication**: Not required

**Response**:
```json
{
  "success": true,
  "message": "API is healthy",
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00",
    "version": "1.0.0"
  }
}
```

### 2. API Documentation

Get comprehensive API documentation.

**Endpoint**: `GET /docs`  
**Authentication**: Not required

**Response**: Complete API documentation in JSON format.

### 3. Create Lead

Create a new CRM lead with Btaskee service details.

**Endpoint**: `POST /lead/create`  
**Authentication**: Required

**Request Body**:
```json
{
  "api_key": "your_api_key",
  "lead_data": {
    "name": "Lead Name",
    "partner_name": "Customer Name",
    "email_from": "<EMAIL>",
    "phone": "+84901234567",
    "mobile": "+84901234567",
    "street": "123 Street Name",
    "city": "Ho Chi Minh City",
    "country_code": "VN",
    "state_name": "Ho Chi Minh City",
    "district_name": "District 1",
    "ward_name": "Ward 1",
    "btaskee_service_type": "Air Conditioner",
    "date_booking": "2024-12-31 10:00:00",
    "description": "Service description",
    "aircon_details": [
      {
        "type_name": "split_ac",
        "hp_min": 1,
        "hp_max": 2,
        "quantity": 2,
        "gas_refill": 1,
        "note": "Living room and bedroom"
      }
    ]
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "Lead created successfully",
  "data": {
    "lead_id": 123,
    "lead_name": "Lead Name"
  }
}
```

### 4. Get Lead

Retrieve lead details by ID.

**Endpoint**: `GET /lead/{id}`  
**Authentication**: Required

**Parameters**:
- `id` (integer): Lead ID
- `api_key` (string): API key

**Response**:
```json
{
  "success": true,
  "message": "Lead retrieved successfully",
  "data": {
    "id": 123,
    "name": "Lead Name",
    "partner_name": "Customer Name",
    "email_from": "<EMAIL>",
    "phone": "+84901234567",
    "mobile": "+84901234567",
    "street": "123 Street Name",
    "city": "Ho Chi Minh City",
    "country": "Vietnam",
    "country_code": "VN",
    "state": "Ho Chi Minh City",
    "district": "District 1",
    "ward": "Ward 1",
    "btaskee_service": "Air Conditioner",
    "service_type": "Air Conditioner",
    "date_booking": "2024-12-31 10:00:00",
    "total_price": 500000.0,
    "description": "Service description",
    "stage": "New",
    "create_date": "2024-01-15 10:30:00",
    "write_date": "2024-01-15 10:30:00",
    "aircon_details": [
      {
        "id": 456,
        "type": "split_ac",
        "type_text_en": "Split Air Conditioner",
        "hp_range": "Min 1 HP",
        "hp_min": 1,
        "hp_max": 2,
        "quantity": 2,
        "gas_refill": 1,
        "note": "Living room and bedroom",
        "index": 1
      }
    ]
  }
}
```

### 5. Get Leads List

Retrieve a list of leads with optional filters.

**Endpoint**: `GET /leads`  
**Authentication**: Required

**Parameters**:
- `api_key` (string): API key
- `limit` (integer, optional): Number of records to return (default: 20)
- `offset` (integer, optional): Number of records to skip (default: 0)
- `service_type` (string, optional): Filter by service type
- `partner_name` (string, optional): Filter by customer name (partial match)
- `date_from` (datetime, optional): Filter by booking date from
- `date_to` (datetime, optional): Filter by booking date to

**Response**:
```json
{
  "success": true,
  "message": "Leads retrieved successfully",
  "data": {
    "leads": [...],
    "total_count": 150,
    "limit": 20,
    "offset": 0
  }
}
```

### 6. Update Lead

Update lead details.

**Endpoint**: `PUT /lead/{id}/update` or `POST /lead/{id}/update`  
**Authentication**: Required

**Request Body**:
```json
{
  "api_key": "your_api_key",
  "update_data": {
    "name": "Updated Lead Name",
    "phone": "+84901234567",
    "date_booking": "2024-12-31 15:00:00",
    "description": "Updated description"
  }
}
```

**Allowed Update Fields**:
- `name`, `partner_name`, `email_from`, `phone`, `mobile`
- `street`, `city`, `description`, `date_booking`

### 7. Get Lead Price

Calculate price for a lead using Btaskee API.

**Endpoint**: `GET /lead/{id}/price`  
**Authentication**: Required

**Parameters**:
- `id` (integer): Lead ID
- `api_key` (string): API key

**Response**:
```json
{
  "success": true,
  "message": "Price calculated successfully",
  "data": {
    "price": 500000,
    "currency": "VND",
    "details": {...}
  }
}
```

### 8. Get Services

Retrieve list of available Btaskee services.

**Endpoint**: `GET /services`  
**Authentication**: Required

**Response**:
```json
{
  "success": true,
  "message": "Services retrieved successfully",
  "data": [
    {
      "id": 1,
      "service_type": "Air Conditioner",
      "service_types": "air_conditioner",
      "api_call": "https://api.btaskee.com/v1/aircon/price"
    }
  ]
}
```

### 9. Get Air Conditioner Types

Retrieve list of air conditioner types with translations.

**Endpoint**: `GET /aircon-types`  
**Authentication**: Required

**Response**:
```json
{
  "success": true,
  "message": "Air conditioner types retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "split_ac",
      "text_en": "Split Air Conditioner",
      "text_vi": "Máy lạnh treo tường",
      "text_ko": "벽걸이 에어컨",
      "text_th": "แอร์แยกส่วน",
      "hp_ranges": [
        {
          "id": 1,
          "name": "Min 1 HP",
          "min_hp": 1,
          "max_hp": 2
        }
      ]
    }
  ]
}
```

### 10. Get HP Ranges

Retrieve list of horsepower ranges.

**Endpoint**: `GET /hp-ranges`  
**Authentication**: Required

**Response**:
```json
{
  "success": true,
  "message": "HP ranges retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Min 1 HP",
      "min_hp": 1,
      "max_hp": 2
    }
  ]
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `INVALID_API_KEY` | API key is missing or invalid |
| `INVALID_JSON` | Request body contains invalid JSON |
| `MISSING_LEAD_DATA` | Lead data is missing from request |
| `MISSING_REQUIRED_FIELDS` | Required fields are missing |
| `MISSING_UPDATE_DATA` | Update data is missing |
| `LEAD_NOT_FOUND` | Lead with specified ID not found |
| `VALIDATION_ERROR` | Data validation failed |
| `ACCESS_DENIED` | Insufficient permissions |
| `PRICE_CALCULATION_ERROR` | Price calculation failed |
| `INTERNAL_ERROR` | Internal server error |

## Date/Time Format

All date/time fields should use one of these formats:
- `YYYY-MM-DD HH:MM:SS` (e.g., "2024-12-31 10:00:00")
- `YYYY-MM-DDTHH:MM:SS` (e.g., "2024-12-31T10:00:00")
- `YYYY-MM-DD HH:MM` (e.g., "2024-12-31 10:00")
- `YYYY-MM-DD` (e.g., "2024-12-31")

## Rate Limiting

Currently, there are no rate limits implemented. However, it's recommended to:
- Limit requests to 100 per minute per API key
- Implement exponential backoff for failed requests
- Cache configuration data (services, AC types, HP ranges)

## Examples

See `examples/api_usage_examples.py` for complete usage examples in Python.

## Support

For API support and questions, please contact the development team or refer to the module documentation.
