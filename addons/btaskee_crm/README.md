# Btaskee CRM Module

This module extends Odoo's CRM functionality to integrate with Btaskee services, specifically for air conditioner maintenance and repair services.

## Features

### Models

1. **Btaskee Service** (`btaskee.service`)
   - Service type configuration
   - API endpoint configuration
   - Integration settings

2. **Air Conditioner HP Range** (`aircon.hp.range`)
   - Horsepower range definitions
   - Min/Max HP specifications
   - Auto-computed display names

3. **Air Conditioner Type** (`aircon.type`)
   - AC type definitions with multilingual support
   - English, Vietnamese, Korean, Thai translations
   - HP range associations

4. **Air Conditioner Line** (`aircon.line`)
   - Service line items for leads
   - Quantity and gas refill specifications
   - Automatic indexing

### Views and Menus

#### Main Menu Structure
```
Btaskee CRM
├── Services
│   ├── Btaskee Leads
│   ├── AC Leads
│   ├── Bookings Calendar
│   ├── Btaskee Services
│   └── AC Service Lines
└── Configuration
    ├── AC Types
    └── HP Ranges
```

#### Key Views

1. **Btaskee Leads**
   - Enhanced CRM lead views with Btaskee-specific fields
   - Service booking management
   - Price calculation integration

2. **Bookings Calendar**
   - Calendar view for scheduled services
   - Color-coded by service type
   - Quick booking overview

3. **Configuration Views**
   - AC Types with multilingual support
   - HP Range management
   - Service configuration

### Enhanced CRM Lead Features

- **Btaskee Service Tab**: Added to standard CRM lead form
- **Location Fields**: District and Ward selection
- **Air Conditioner Details**: Inline editing of AC specifications
- **Price Calculation**: Integration with Btaskee API
- **Booking Management**: Date/time scheduling

### API Integration

- JSON payload preparation for Btaskee API
- Error handling and validation
- Comprehensive logging for debugging
- Timeout and network error management

## Installation

1. Copy the module to your Odoo addons directory
2. Update the module list
3. Install the "bTaskee CRM" module
4. Configure Btaskee services with API endpoints

## Usage

1. **Configure Services**: Set up Btaskee services with API endpoints
2. **Define AC Types**: Create air conditioner types with translations
3. **Set HP Ranges**: Define horsepower ranges for different AC types
4. **Create Leads**: Use enhanced CRM leads with Btaskee integration
5. **Schedule Services**: Use the booking calendar for service scheduling

## Demo Data

The module includes demo data with:
- Sample Btaskee services
- Common AC types (Split, Cassette, Window, Central)
- HP ranges (1-5 HP)
- Sample leads with AC service details
- Vietnam location data (Ho Chi Minh City)

## Technical Notes

- All models include proper access rights
- Views are responsive and user-friendly
- Comprehensive error handling in API integration
- Multilingual support for customer-facing content
- Proper validation and data integrity checks

## Dependencies

- `crm`: Odoo CRM module
- `base`: Odoo base module

## Version

17.0.0.0 - Compatible with Odoo 17.0
