<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Create User Group -->
        <record id="group_create_user" model="res.groups">
            <field name="name">Create User</field>
            <field name="category_id" ref="base.module_category_human_resources"/>
            <field name="comment">Users with this group can create internal users with limited permissions.</field>
        </record>


        <record id="group_accouting_TMDV" model="res.groups">
            <field name="name">Accouting TMDV</field>
            <field name="comment">Use for accouting confirm lead.</field>
        </record>

        <record id="group_accouting_TNHH" model="res.groups">
            <field name="name">Accouting TNHH</field>
            <field name="comment">Use for accouting confirm lead.</field>
        </record>

    </data>
</odoo>
