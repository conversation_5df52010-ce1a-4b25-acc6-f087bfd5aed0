# Create User Group Functionality

## 🎯 Overview

I've implemented a new security group called `create_user` that allows designated users to create internal users with limited permissions. Users created through this system will automatically be assigned only the "User: Own Documents Only" group, ensuring they have basic access without administrative privileges.

## ✅ What's New

### **New Security Group: `create_user`**
- **Group Name**: Create User
- **Category**: Human Resources
- **Purpose**: Allows users to create internal users with limited permissions
- **Access Level**: Can create users but not modify system settings

### **User Creation Wizard**
- **Model**: `user.creation.wizard`
- **Purpose**: Guided interface for creating users with validation
- **Restrictions**: Automatically assigns only basic user permissions

### **Enhanced Security**
- **Permission Validation**: Checks user permissions before allowing user creation
- **Group Restriction**: Automatically limits created users to "User: Own Documents Only"
- **Data Validation**: Validates email, login uniqueness, and required fields

## 🔧 Technical Implementation

### **Security Group Definition**
```xml
<record id="group_create_user" model="res.groups">
    <field name="name">Create User</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="comment">Users with this group can create internal users with limited permissions.</field>
</record>
```

### **Access Rights**
```csv
"access_res_users_create_user","res.users create_user","base.model_res_users",btaskee_crm.group_create_user,1,1,1,0
"access_res_groups_create_user","res.groups create_user","base.model_res_groups",btaskee_crm.group_create_user,1,0,0,0
```

### **User Creation Logic**
```python
def create_user(self):
    # Check permissions
    if not self.env.user.has_group('btaskee_crm.group_create_user'):
        raise AccessError(_("You don't have permission to create users."))
    
    # Get basic user group
    user_group = self.env.ref('base.group_user')
    
    # Create user with limited permissions
    user_vals = {
        'name': self.name,
        'login': self.login,
        'email': self.email,
        'groups_id': [(6, 0, [user_group.id])],  # Only basic user group
        'active': True,
    }
    
    new_user = self.env['res.users'].create(user_vals)
```

## 📱 User Interface

### **Access Points**
1. **Settings → Users & Companies → Users** (Create User button)
2. **Main Menu → Create User** (for users with create_user group)
3. **Users list view** (Create User button in header)
4. **Users form view** (Create New User button)

### **User Creation Wizard**
```
┌─────────────────────────────────────┐
│ Create New Internal User            │
├─────────────────────────────────────┤
│ Full Name: [________________]       │
│ Login:     [________________]       │
│ Email:     [________________]       │
│ Phone:     [________________]       │
│ Company:   [________________]       │
├─────────────────────────────────────┤
│ Note: User will be created with     │
│ "User: Own Documents Only" perms    │
├─────────────────────────────────────┤
│ [Create User] [Cancel]              │
└─────────────────────────────────────┘
```

## 🛡️ Security Features

### **Permission Validation**
- **Group Check**: Validates user has `create_user` group before allowing access
- **Access Control**: Prevents unauthorized user creation
- **Error Handling**: Clear error messages for permission issues

### **Data Validation**
- **Login Uniqueness**: Checks if login already exists
- **Email Uniqueness**: Validates email is not already in use
- **Email Format**: Basic email format validation
- **Required Fields**: Ensures all required fields are filled

### **Group Restriction**
- **Automatic Assignment**: Created users get only "User: Own Documents Only" group
- **Admin Prevention**: Prevents assignment of admin or system groups
- **Security Override**: System admins can still create users with full permissions

## 📊 User Permissions

### **Users with `create_user` Group Can:**
- ✅ **Create new internal users**
- ✅ **View user list** (read access)
- ✅ **Edit user details** (write access)
- ✅ **Access user creation wizard**
- ✅ **View groups** (read-only)

### **Users with `create_user` Group Cannot:**
- ❌ **Delete users** (no unlink permission)
- ❌ **Modify user groups** (except during creation)
- ❌ **Create/modify security groups**
- ❌ **Access system administration**

### **Created Users Will Have:**
- ✅ **"User: Own Documents Only" group**
- ✅ **Basic system access**
- ✅ **Access to their own documents**
- ✅ **Standard user interface**

### **Created Users Will NOT Have:**
- ❌ **Administrative privileges**
- ❌ **System configuration access**
- ❌ **User management capabilities**
- ❌ **Advanced security permissions**

## 🔄 Workflow

### **Step 1: Assign create_user Group**
```
Administrator assigns create_user group to designated users
↓
Users gain access to user creation functionality
```

### **Step 2: User Creation Process**
```
User clicks "Create User" button
↓
User Creation Wizard opens
↓
User fills in required information
↓
System validates data and permissions
↓
New user created with basic permissions
↓
Success message with user details
```

### **Step 3: New User Access**
```
New user receives login credentials
↓
User logs in and sets password
↓
User has access to basic system features
↓
User can access only their own documents
```

## 🧪 Testing

### **Test Scenarios**
1. **Permission Test**: Verify only users with `create_user` group can access wizard
2. **Validation Test**: Test email/login uniqueness validation
3. **Group Assignment Test**: Verify created users only get basic user group
4. **Access Test**: Confirm created users have appropriate access levels
5. **Security Test**: Ensure admin groups cannot be assigned by create_user users

### **Test User Creation**
```python
# Test creating user with create_user group
user_with_permission = self.env['res.users'].create({
    'name': 'User Creator',
    'login': 'user.creator',
    'groups_id': [(6, 0, [self.env.ref('btaskee_crm.group_create_user').id])]
})

# Test wizard access
wizard = self.env['user.creation.wizard'].with_user(user_with_permission).create({
    'name': 'Test User',
    'login': 'test.user',
    'email': '<EMAIL>'
})

# Test user creation
result = wizard.create_user()
```

## 📋 Configuration

### **Assign create_user Group**
1. **Go to Settings → Users & Companies → Users**
2. **Select the user** who should be able to create users
3. **Edit user** and go to "Access Rights" tab
4. **Add "Create User" group** to their permissions
5. **Save** the user

### **Menu Access**
- **Menu item** appears automatically for users with `create_user` group
- **Located under** Settings → Users & Companies
- **Also accessible** from Users list/form views

## 🎯 Benefits

### **🔐 Enhanced Security**
- **Controlled user creation** without full admin access
- **Automatic permission limitation** prevents privilege escalation
- **Clear separation** between user creation and system administration

### **👥 Delegated Administration**
- **HR departments** can create users without IT involvement
- **Team leaders** can add team members independently
- **Reduced admin workload** for routine user creation

### **🛡️ Risk Mitigation**
- **Prevents accidental** admin privilege assignment
- **Ensures consistent** user permission structure
- **Maintains security** while enabling delegation

## 🚀 Usage Examples

### **HR Department Use Case**
```
HR Manager has create_user group
↓
New employee joins company
↓
HR Manager creates user account through wizard
↓
New employee gets basic access automatically
↓
IT can later adjust permissions if needed
```

### **Team Leader Use Case**
```
Team Leader has create_user group
↓
New team member needs system access
↓
Team Leader creates user account
↓
Team member can start working immediately
↓
No waiting for IT department
```

## 🎉 Result

Your Btaskee CRM module now provides:

- ✅ **Secure user creation** delegation capability
- ✅ **Automatic permission management** for created users
- ✅ **User-friendly wizard** interface
- ✅ **Comprehensive validation** and error handling
- ✅ **Clear security boundaries** between user creation and administration
- ✅ **Flexible access control** through group membership

Users with the `create_user` group can now safely create internal users with appropriate permissions, streamlining user management while maintaining security! 🔐
