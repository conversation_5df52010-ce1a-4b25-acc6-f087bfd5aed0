# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_outlook
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-29 10:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"
msgstr "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                        Connect your Outlook account"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                        Verbinden Sie Ihr Outlook-Konto"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
msgid ""
"<span invisible=\"server_type != 'outlook' or not microsoft_outlook_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""
"<span invisible=\"server_type != 'outlook' or not microsoft_outlook_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Outlook-Token Gültig\n"
"                    </span>"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<span invisible=\"smtp_authentication != 'outlook' or not microsoft_outlook_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""
"<span invisible=\"smtp_authentication != 'outlook' or not microsoft_outlook_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Outlook-Token Gültig\n"
"                    </span>"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token. %s"
msgstr "Beim Abrufen des Zugriffstokens ist ein Fehler aufgetreten. %s"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__smtp_authentication
msgid "Authenticate with"
msgstr "Authentifizieren mit"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "Authentication URI"
msgstr "Authentifizierungs-URI"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Connect your Outlook account with the OAuth Authentication process.  \n"
"By default, only a user with a matching email address will be able to use this server. To extend its use, you should set a \"mail.default.from\" system parameter."
msgstr ""
"Verbinden Sie Ihr Outlook-Konto mit dem OAuth-Authentifizierungsverfahren. \n"
"Standardmäßig kann nur ein Benutzer mit einer passenden E-Mail-Adresse diesen Server verwenden. Um die Nutzung zu erweitern, sollten Sie einen Systemparameter „mail.default.from“ festlegen."

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid ""
"Connect your personal Outlook account using OAuth. \n"
"You will be redirected to the Outlook login page to accept the permissions."
msgstr ""
"Verbinden Sie Ihr persönliches Outlook-Konto über OAuth. \n"
"Sie werden zur Outlook-Anmeldeseite weitergeleitet, um die Berechtigungen zu akzeptieren."

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.microsoft_outlook_oauth_error
msgid "Go back to your mail server"
msgstr "Gehen Sie zurück in Ihren E-Mail-Server"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID"
msgstr "ID"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID of your Outlook app"
msgstr "ID Ihrer Outlook-App"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Posteingangsserver"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Incorrect Connection Security for Outlook mail server %r. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""
"Falsche Verbindungssicherheit für Outlook-Mailserver %r. Bitte setzen Sie "
"ihn auf „TLS (STARTTLS)“."

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__is_microsoft_outlook_configured
msgid "Is Outlook Credential Configured"
msgstr "Sind die Outlook-Anmeldeinformationen konfiguriert"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_ir_mail_server
msgid "Mail Server"
msgstr "Mailserver"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_microsoft_outlook_mixin
msgid "Microsoft Outlook Mixin"
msgstr "Microsoft-Outlook-Mixin"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Only the administrator can link an Outlook mail server."
msgstr "Nur der Administrator kann einen Outlook-Mailserver verbinden."

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token
msgid "Outlook Access Token"
msgstr "Outlook-Zugriffstoken"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token_expiration
msgid "Outlook Access Token Expiration Timestamp"
msgstr "Ablaufdatum des Outlook-Zugriffstokens"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr "Outlook-Client-ID"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr "Outlook-Client-Geheimnis"

#. module: microsoft_outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__fetchmail_server__server_type__outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__ir_mail_server__smtp_authentication__outlook
msgid "Outlook OAuth Authentication"
msgstr "Outlook-OAuth-Authentifizierung"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_refresh_token
msgid "Outlook Refresh Token"
msgstr "Outlook-Aktualisierungstoken"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please configure your Outlook credentials."
msgstr "Bitte konfigurieren Sie Ihre Outlook-Anmeldedaten."

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please connect with your Outlook account before using it."
msgstr "Bitte verbinden Sie Ihr Outlook-Konto, bevor Sie es verwenden."

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please fill the \"Username\" field with your Outlook/Office365 username "
"(your email address). This should be the same account as the one used for "
"the Outlook OAuthentication Token."
msgstr ""
"Bitte geben Sie in das Feld „Benutzername“ Ihren "
"Outlook/Office365-Benutzernamen (Ihre E-Mail-Adresse) ein. Dies sollte "
"dasselbe Konto sein, das auch für das Outlook-OAuthentication-Token "
"verwendet wird."

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please leave the password field empty for Outlook mail server %r. The OAuth "
"process does not require it"
msgstr ""
"Bitte lassen Sie das Passwortfeld für den Outlook-Mailserver %r leer. Der "
"OAuth-Prozess benötigt es nicht"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "Read More"
msgstr "Mehr lesen"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid "SSL is required for the server %r."
msgstr "SSL ist für den Server %r erforderlich."

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret"
msgstr "Geheimnis"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret of your Outlook app"
msgstr "Geheimnis Ihrer Outlook-App"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Servertyp"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"Setup your Outlook API credentials in the general settings to link a Outlook"
" account."
msgstr ""
"Richten Sie Ihre Outlook-API-Anmeldedaten in den allgemeinen Einstellungen "
"ein, um ein Outlook-Konto zu verknüpfen."

#. module: microsoft_outlook
#: model:ir.model.fields,help:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "The URL to generate the authorization code from Outlook"
msgstr "Die URL zum Generieren des Autorisierungscodes aus Outlook"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Unknown error."
msgstr "Unbekannter Fehler."
